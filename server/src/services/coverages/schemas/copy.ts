import {Type, ObjectIdSchema } from '@feathersjs/typebox';
import {coverageCalcSchema} from './benefits.js';
import {geoJsonSchema, imageSchema, videoSchema} from '../../../utils/common/typebox-schemas.js';

export const coverCopySchema = Type.Object(
    {
        // spread coverageCalcSchema (already a Type.Object)
        ...Object.fromEntries(
            Object.entries((coverageCalcSchema as any).properties).map(([k, v]) => [
                k,
                Type.Optional(v as any),
            ])
        ),

        carrierLogo: Type.Optional(imageSchema),
        documents: Type.Optional(Type.Array(imageSchema)),
        postTax: Type.Optional(Type.Boolean()),
        video: Type.Optional(videoSchema),
        geo: Type.Optional(geoJsonSchema),
        issuer: Type.Optional(ObjectIdSchema()), // orgId of insurance company
        org: Type.Optional(ObjectIdSchema()),    // added by
        lastSync: Type.Optional(Type.String()),
        provider: Type.Optional(ObjectIdSchema()),
        template: Type.Optional(Type.Boolean()),
        fromTemplate: Type.Optional(ObjectIdSchema()),
        public: Type.Optional(Type.Boolean()),
        sim: Type.Optional(Type.Boolean()),
        group_sim_only: Type.Optional(
            Type.Boolean({
                description:
                    "For products only available to groups - even if it's an individual contract. Won't show in individual shop experience not tied to a group",
            })
        ),
        contract: Type.Optional(ObjectIdSchema()),
        listBillDiscount: Type.Optional(Type.Number()), // 0 to 100 percent

        av: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        value: Type.Optional(Type.Number()), // 0 to 100
                        by: Type.Optional(ObjectIdSchema()),
                        at: Type.Optional(Type.Any()),
                    },
                    { additionalProperties: true }
                )
            )
        ),

        dpc: Type.Optional(Type.Boolean()),
        ichra: Type.Optional(Type.Boolean()),
        shop: Type.Optional(Type.Boolean()),

        adj: Type.Optional(
            Type.Object(
                {
                    autoMax: Type.Optional(Type.Number()),
                    authProviders: Type.Optional(Type.Array(ObjectIdSchema())),
                },
                { additionalProperties: true }
            )
        ),

        networks: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        coins_discount: Type.Optional(Type.Number()),
                        coins_discount_type: Type.Optional(
                            Type.String({ enum: ['percent', 'flat'] })
                        ),
                        ded_discount: Type.Optional(Type.Number()),
                        ded_discount_type: Type.Optional(
                            Type.String({ enum: ['percent', 'flat'] })
                        ),
                    },
                    { additionalProperties: true }
                )
            )
        ),

        disability: Type.Optional(
            Type.Object(
                {
                    coverOverRequiredAge: Type.Optional(Type.Boolean()),
                    incomeLimit: Type.Optional(Type.Number()),
                },
                { additionalProperties: true }
            )
        ),
    },
    { additionalProperties: true }
);
